
Here’s the complete DBML (Database Markup Language) definition for your DogSchool Training App schema.

You can paste this into tools like:

https://prisma-editor.bahumaish.com/schema/16534

📦 Or use it in code with DBML parsers

## Database Development Workflows

### 1. Local Development (Quick prototyping)
```bash
npx prisma db push    # Push schema changes directly to local DB
npm run db:generate   # Update Prisma client
```

### 2. Shared Development (Team collaboration)
```bash
npm run db:migrate:dev   # Create migration for schema changes
git add prisma/migrations && git commit   # Commit migration files
```

## Tools

npx prisma generate

Viuslize: https://prisma-editor.bahumaish.com/schema/16534
Info: you need to remove the client and db from the schema

not that nice: https://prismaliser.app/

npm run db:generate