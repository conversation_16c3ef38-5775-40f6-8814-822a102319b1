# 🏗️ Architecture Documentation

## **System Overview**

The Petagotchi application uses a **dual adapter architecture** that enables deployment to both web and mobile platforms from a single codebase.

```
┌─────────────────────────────────────────────────────────────┐
│                    Petagotchi Application                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐              ┌─────────────────┐       │
│  │   Web Client    │              │  Android App    │       │
│  │  (SvelteKit)    │              │  (Capacitor)    │       │
│  └─────────────────┘              └─────────────────┘       │
│           │                                │                │
│           │ HTTP/HTTPS                     │ HTTP/HTTPS     │
│           │                                │                │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Node.js API Server                         ││
│  │                (SvelteKit)                              ││
│  └─────────────────────────────────────────────────────────┘│
│                              │                              │
│                              │ Prisma ORM                   │
│                              │                              │
│                    ┌─────────────────┐                      │
│                    │   SQLite DB     │                      │
│                    │  (Production:   │                      │
│                    │  PostgreSQL)    │                      │
│                    └─────────────────┘                      │
└─────────────────────────────────────────────────────────────┘
```

---

## **🔧 Adapter Strategy**

### **Dual Adapter Configuration**

The application uses **two SvelteKit adapters** depending on the deployment target:

#### **1. Node.js Adapter** (`@sveltejs/adapter-node`)

- **Purpose**: Web application + API server
- **Output**: `build-node/` directory
- **Features**:
  - Server-side rendering (SSR)
  - API endpoints (`/api/*`)
  - Session management
  - Database operations
  - Authentication handling

#### **2. Static Adapter** (`@sveltejs/adapter-static`)

- **Purpose**: Single Page Application for mobile
- **Output**: `build-static/` directory
- **Features**:
  - Pre-rendered static files
  - Client-side routing
  - Consumes APIs from Node.js server
  - Capacitor-compatible

### **Build Configuration**

```javascript
// svelte.config.js
import adapter from "@sveltejs/adapter-node";
import staticAdapter from "@sveltejs/adapter-static";

const config = {
  kit: {
    adapter:
      process.env.VITE_ADAPTER === "static"
        ? staticAdapter({
            pages: "build-static",
            assets: "build-static",
            fallback: "index.html",
          })
        : adapter({
            out: "build-node",
          }),
  },
};
```

---

## **🗄️ Database Architecture**

### **Technology Stack**

- **ORM**: Prisma
- **Development**: SQLite (`./prisma/dev.db`)
- **Production**: SQLite or PostgreSQL
- **Migrations**: Prisma Migrate

### **Database Schema**

```prisma
// prisma/schema.prisma
model User {
  id           String    @id @default(uuid())
  email        String    @unique
  name         String
  password_hash String
  role         String    @default("user")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  sessions     Session[]
}

model Session {
  id        String   @id @default(uuid())
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### **Data Flow**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│  Auth Server    │───▶│  Auth Repository│
│                 │    │                 │    │                 │
│ - Login Form    │    │ - Rate Limiting │    │ - User CRUD     │
│ - Registration  │    │ - Password Hash │    │ - Session CRUD  │
│ - Session Mgmt  │    │ - Session Mgmt  │    │ - Database Ops  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   Prisma ORM    │
                                               │                 │
                                               │ - Type Safety   │
                                               │ - Query Builder │
                                               │ - Migrations    │
                                               └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   SQLite DB     │
                                               │                 │
                                               │ - Users Table   │
                                               │ - Sessions Table│
                                               └─────────────────┘
```

---

## **🔐 Authentication Architecture**

### **Security Components**

```
┌─────────────────────────────────────────────────────────────┐
│                    Authentication Flow                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                 │
│  │  Rate Limiter   │    │  Input Validator│                 │
│  │                 │    │                 │                 │
│  │ - 5 attempts    │    │ - Zod Schemas   │                 │
│  │ - 15 min window │    │ - Sanitization  │                 │
│  │ - IP + Email    │    │ - Type Safety   │                 │
│  └─────────────────┘    └─────────────────┘                 │
│           │                       │                         │
│           ▼                       ▼                         │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Auth Server                                ││
│  │                                                         ││
│  │ - Argon2 Password Hashing                               ││
│  │ - Session Management                                    ││
│  │ - User Registration/Login                               ││
│  │ - Timing Attack Protection                              ││
│  └─────────────────────────────────────────────────────────┘│
│                              │                              │
│                              ▼                              │
│                    ┌─────────────────┐                      │
│                    │   Session Store │                      │
│                    │                 │                      │
│                    │ - Database      │                      │
│                    │ - Expiration    │                      │
│                    │ - Cleanup       │                      │
│                    └─────────────────┘                      │
└─────────────────────────────────────────────────────────────┘
```

### **Security Features**

1. **Password Security**

   - Argon2id hashing algorithm
   - Memory-hard function (19MB memory cost)
   - GPU-resistant
   - Configurable time cost

2. **Session Security**

   - Database-stored sessions
   - Automatic expiration (30 days)
   - Secure cookie flags
   - HttpOnly + SameSite protection

3. **Rate Limiting**

   - 5 attempts per 15 minutes
   - IP + email combination tracking
   - Automatic cleanup of expired entries

4. **Input Validation**
   - Zod schema validation
   - Email sanitization
   - Password complexity requirements
   - XSS protection

---

## **🌐 API Architecture**

### **Endpoint Structure**

```
/api/
├── auth/
│   ├── register     # POST - User registration
│   ├── login        # POST - User authentication
│   ├── logout       # POST - Session termination
│   └── me           # GET  - Current user info
├── health           # GET  - System health check
└── [future endpoints]
```

### **Request/Response Flow**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Client      │    │   SvelteKit     │    │   Auth Server   │
│                 │    │    Hooks        │    │                 │
│ 1. HTTP Request │───▶│                 │───▶│ 3. Business     │
│                 │    │ 2. CORS         │    │    Logic        │
│                 │    │    Auth         │    │                 │
│ 6. HTTP Response│◀───│    Logging      │◀───│ 4. Database     │
│                 │    │                 │    │    Operations   │
│                 │    │ 5. Response     │    │                 │
│                 │    │    Headers      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Middleware Stack**

1. **CORS Handler**

   - Origin validation
   - Preflight handling
   - Mobile app support

2. **Authentication Handler**

   - Session validation
   - User context injection
   - Protected route enforcement

3. **Logging Handler**
   - Request/response logging
   - Error tracking
   - Performance monitoring

---

## **📱 Mobile Integration**

### **Capacitor Configuration**

```typescript
// capacitor.config.ts
import { CapacitorConfig } from "@capacitor/cli";

const config: CapacitorConfig = {
  appId: "com.petagotchi.app",
  appName: "Petagotchi",
  webDir: "build-static",
  server: {
    androidScheme: "https",
  },
};
```

### **API Communication**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Android App    │    │   Capacitor     │    │  Node.js API    │
│  (WebView)      │    │   Bridge        │    │   Server        │
│                 │    │                 │    │                 │
│ - Static HTML   │───▶│ - HTTP Client   │───▶│ - API Endpoints │
│ - JavaScript    │    │ - Native APIs   │    │ - Database      │
│ - CSS           │    │ - Plugins       │    │ - Auth          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## **🔄 Development vs Production**

### **Development Mode**

```
┌─────────────────────────────────────────────────────────────┐
│                    Development Setup                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐              ┌─────────────────┐       │
│  │   Vite Dev      │              │  Android Dev    │       │
│  │   Server        │              │  (Capacitor)    │       │
│  │ localhost:5173  │              │                 │       │
│  └─────────────────┘              └─────────────────┘       │
│           │                                │                │
│           │ Hot Reload                     │ Live Reload    │
│           │                                │                │
│           ▼                                ▼                │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              SvelteKit Dev Server                       ││
│  │            (Node.js Adapter)                            ││
│  └─────────────────────────────────────────────────────────┘│
│                              │                              │
│                              ▼                              │
│                    ┌─────────────────┐                      │
│                    │   SQLite Dev    │                      │
│                    │   ./dev.db      │                      │
│                    └─────────────────┘                      │
└─────────────────────────────────────────────────────────────┘
```

### **Production Mode**

```
┌─────────────────────────────────────────────────────────────┐
│                    Production Setup                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐              ┌─────────────────┐       │
│  │   Web Client    │              │  Android APK    │       │
│  │   (Browser)     │              │  (Play Store)   │       │
│  └─────────────────┘              └─────────────────┘       │
│           │                                │                │
│           │ HTTPS                          │ HTTPS          │
│           │                                │                │
│           ▼                                ▼                │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Node.js Production Server                  ││
│  │                (PM2/Docker)                             ││
│  └─────────────────────────────────────────────────────────┘│
│                              │                              │
│                              ▼                              │
│                    ┌─────────────────┐                      │
│                    │  Production DB  │                      │
│                    │ SQLite/Postgres │                      │
│                    └─────────────────┘                      │
└─────────────────────────────────────────────────────────────┘
```

---

## **📊 Performance Considerations**

### **Optimization Strategies**

1. **Build Optimization**

   - Code splitting by adapter
   - Tree shaking unused code
   - Asset optimization

2. **Database Performance**

   - Connection pooling (PostgreSQL)
   - Query optimization
   - Index strategy

3. **Caching Strategy**

   - Static asset caching
   - API response caching
   - Session caching

4. **Mobile Performance**
   - Lazy loading
   - Offline support
   - Native plugin optimization

### **Scalability Considerations**

1. **Horizontal Scaling**

   - Stateless API design
   - Load balancer compatibility
   - Database connection management

2. **Vertical Scaling**

   - Memory optimization
   - CPU utilization
   - I/O performance

3. **Future Enhancements**
   - Redis session store
   - CDN integration
   - Microservices migration
