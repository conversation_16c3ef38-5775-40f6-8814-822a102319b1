# 🚀 Netlify Deployment Guide

## Overview

This guide will help you deploy your SvelteKit application to Netlify with serverless functions for API endpoints.

## Prerequisites

1. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **Database**: You'll need a database (SQLite file or external database)

## Step 1: Prepare Your Repository

### 1.1 Verify Configuration Files

Ensure these files are in your repository:

- `netlify.toml` - Netlify configuration
- `netlify/functions/` - Serverless functions
- `package.json` - With `build:netlify` script

### 1.2 Environment Variables

Set up these environment variables in Netlify dashboard:

```bash
# Required
NODE_ENV=production
VITE_ADAPTER=static
DATABASE_URL=your-database-url
SESSION_SECRET=your-secure-session-secret

# Optional
PUBLIC_API_URL=https://your-app.netlify.app
PUBLIC_ALLOWED_ORIGINS=https://your-app.netlify.app,capacitor://localhost
LOG_LEVEL=info
```

## Step 2: Deploy to Netlify

### Option A: Deploy from Git (Recommended)

1. **Connect Repository**:

   - Go to Netlify dashboard
   - Click "New site from Git"
   - Choose your GitHub repository
   - Select the branch (usually `main`)

2. **Configure Build Settings**:

   - **Build command**: `npm run build:netlify`
   - **Publish directory**: `build-static`
   - **Base directory**: `src/web` (if your SvelteKit app is in a subdirectory)

3. **Set Environment Variables**:

   - Go to Site settings → Environment variables
   - Add all variables from Step 1.2

4. **Deploy**:
   - Click "Deploy site"
   - Wait for build to complete

### Option B: Manual Deploy

1. **Build Locally**:

   ```bash
   cd src/web
   npm run build:netlify
   ```

2. **Deploy to Netlify**:
   - Drag and drop the `build-static` folder to Netlify
   - Or use Netlify CLI: `netlify deploy --prod --dir=build-static`

## Step 3: Configure Custom Domain (Optional)

1. Go to Site settings → Domain management
2. Add your custom domain
3. Configure DNS settings as instructed

## Step 4: Verify Deployment

### Health Check

```bash
curl https://your-app.netlify.app/.netlify/functions/health
```

### Expected Response

```json
{
	"status": "healthy",
	"timestamp": "2024-01-01T00:00:00.000Z",
	"services": {
		"database": "healthy",
		"auth": "healthy"
	},
	"version": "0.0.1"
}
```

## Step 5: Database Setup

### Option A: SQLite File (Development/Testing)

1. **Upload SQLite file**:

   - Use Netlify's file upload feature
   - Or include in your repository (not recommended for production)

2. **Set DATABASE_URL**:
   ```
   DATABASE_URL=file:/tmp/production.db
   ```

### Option B: External Database (Production)

1. **Use a managed database service**:

   - PlanetScale
   - Supabase
   - Railway
   - Neon

2. **Set DATABASE_URL**:
   ```
   DATABASE_URL=postgresql://user:password@host:port/database
   ```

## Step 6: API Endpoints

Your API endpoints are now available at:

- `/api/auth/login` → `/.netlify/functions/auth-login`
- `/api/health` → `/.netlify/functions/health`

### Update Frontend API Calls

Update your frontend code to use the new API paths:

```typescript
// Before
const response = await fetch('/api/auth/login', {
	method: 'POST',
	body: JSON.stringify(data)
});

// After
const response = await fetch('/.netlify/functions/auth-login', {
	method: 'POST',
	body: JSON.stringify(data)
});
```

## Step 7: Environment-Specific Configuration

### Development

```bash
npm run dev:node  # Uses Node.js adapter
```

### Production (Netlify)

```bash
npm run build:netlify  # Uses static adapter + Netlify Functions
```

## Troubleshooting

### Common Issues

1. **Build Fails**:

   - Check build logs in Netlify dashboard
   - Verify all dependencies are in `package.json`
   - Ensure `build:netlify` script exists

2. **API Functions Not Working**:

   - Verify function files are in `netlify/functions/`
   - Check function logs in Netlify dashboard
   - Ensure environment variables are set

3. **Database Connection Issues**:
   - Verify `DATABASE_URL` is correct
   - Check database is accessible from Netlify
   - Ensure Prisma client is generated

### Debug Commands

```bash
# Test build locally
npm run build:netlify

# Test Netlify Functions locally
netlify dev

# Check function logs
netlify functions:list
```

## Performance Optimization

1. **Enable Netlify Edge Functions** (if needed)
2. **Use Netlify Image Optimization**
3. **Configure caching headers**
4. **Enable Netlify Analytics**

## Security Considerations

1. **Environment Variables**: Never commit secrets to Git
2. **CORS**: Configure allowed origins properly
3. **Rate Limiting**: Implement in your functions
4. **Input Validation**: Use Zod schemas in all functions

## Monitoring

1. **Netlify Analytics**: Monitor site performance
2. **Function Logs**: Check for errors
3. **Health Checks**: Set up monitoring for your health endpoint

## Next Steps

1. **Set up CI/CD**: Connect to GitHub for automatic deployments
2. **Add monitoring**: Set up error tracking (Sentry, etc.)
3. **Optimize performance**: Implement caching strategies
4. **Security audit**: Review security configurations

## Support

- **Netlify Docs**: [docs.netlify.com](https://docs.netlify.com)
- **SvelteKit Docs**: [kit.svelte.dev](https://kit.svelte.dev)
- **Function Logs**: Check Netlify dashboard for detailed logs
