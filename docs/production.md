# 🚀 Production Deployment Guide

## **Architecture Overview**

The production deployment uses a **dual adapter strategy**:

1. **Node.js Server** (`build-node/`) - Serves web application + API endpoints
2. **Static Build** (`build-static/`) - Deployed separately for Android/Capacitor apps

The static build consumes APIs from the Node.js server, enabling independent scaling and deployment.

---

## **🔧 Production Build Process**

### **Build Commands**
```bash
# Build Node.js API server
npm run build:node
# Output: build-node/ directory

# Build static Android app  
npm run build:static
# Output: build-static/ directory

# Build both (if needed)
npm run build:node && npm run build:static
```

### **Build Outputs**
```
build-node/           # Node.js server deployment
├── index.js         # Main server entry point
├── client/          # Client-side assets
└── server/          # Server-side chunks

build-static/        # Static SPA deployment
├── index.html       # Main HTML file
├── _app/           # SvelteKit app assets
└── api/            # Pre-rendered API routes (if any)
```

---

## **🗄️ Database Configuration**

### **Production Database Options**

**Option 1: SQLite (Simple)**
```bash
# Environment variable
DATABASE_URL="file:./prisma/production.db"

# Ensure writable directory
mkdir -p /app/data
DATABASE_URL="file:/app/data/production.db"
```

**Option 2: PostgreSQL (Recommended)**
```bash
# Environment variable
DATABASE_URL="************************************/database"

# With connection pooling
DATABASE_URL="************************************/database?connection_limit=10"
```

### **Database Migration**
```bash
# Production migration (no prompts)
npm run db:migrate

# Alternative: Direct Prisma command
npx prisma migrate deploy
```

---

## **🌍 Environment Configuration**

### **Production Environment (.env.production)**
```bash
# Public variables (client-side)
PUBLIC_API_BASE=/api
PUBLIC_API_URL=https://your-domain.com
PUBLIC_ALLOWED_ORIGINS=https://your-domain.com,capacitor://localhost

# Private variables (server-side)
DATABASE_URL="file:./prisma/production.db"
NODE_ENV=production
SESSION_SECRET=your-super-secure-session-secret-here

# Security & Logging
LOG_LEVEL=error
DISABLE_DEBUG_LOGS=true
```

### **Required Environment Variables**
- **DATABASE_URL** - Database connection string
- **PUBLIC_API_URL** - Public API base URL
- **PUBLIC_ALLOWED_ORIGINS** - CORS allowed origins
- **NODE_ENV** - Must be "production"
- **SESSION_SECRET** - Secure random string for sessions

---

## **🚀 Deployment Methods**

### **Method 1: Direct Node.js Deployment**
```bash
# 1. Build application
npm run build:node

# 2. Run database migrations
npm run db:migrate

# 3. Start production server
npm run start:prod
# or
NODE_ENV=production node build/index.js
```

### **Method 2: PM2 Process Manager**
```bash
# Install PM2 globally
npm install -g pm2

# Start with PM2
pm2 start build/index.js --name "petagotchi-api"

# PM2 ecosystem file (ecosystem.config.js)
module.exports = {
  apps: [{
    name: 'petagotchi-api',
    script: 'build/index.js',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}

# Start with ecosystem
pm2 start ecosystem.config.js
```

### **Method 3: Docker Deployment**
```dockerfile
# Dockerfile
FROM node:22-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source and build
COPY . .
RUN npm run build:node

# Create data directory for SQLite
RUN mkdir -p /app/data

# Expose port
EXPOSE 3000

# Start application
CMD ["npm", "run", "start:prod"]
```

```bash
# Build and run Docker container
docker build -t petagotchi-api .
docker run -p 3000:3000 -v /host/data:/app/data petagotchi-api
```

---

## **📱 Android App Deployment**

### **Static Build Deployment**
```bash
# 1. Build static version
npm run build:static

# 2. Deploy build-static/ to CDN/hosting
# Examples:
# - Netlify: drag build-static/ folder
# - Vercel: vercel --prod build-static/
# - AWS S3: aws s3 sync build-static/ s3://bucket-name/
```

### **Capacitor Build Process**
```bash
# 1. Build static app
npm run build:static

# 2. Sync with Capacitor
npm run cap:sync

# 3. Build Android APK
npm run cap:build

# 4. Generate signed APK (Android Studio)
# - Open Android Studio
# - Build > Generate Signed Bundle/APK
```

### **API Configuration for Mobile**
The Android app needs to point to your production API:

```typescript
// In static build, configure API base URL
PUBLIC_API_URL=https://your-api-domain.com
```

---

## **🔐 Security Configuration**

### **HTTPS & SSL**
```bash
# Ensure HTTPS in production
PUBLIC_API_URL=https://your-domain.com

# Cookie security (automatic in production)
# - Secure flag enabled
# - SameSite=None for cross-origin
# - HttpOnly for XSS protection
```

### **CORS Configuration**
```bash
# Allow your domains
PUBLIC_ALLOWED_ORIGINS=https://your-web-domain.com,https://your-api-domain.com,capacitor://localhost

# For mobile apps, always include:
capacitor://localhost
```

### **Rate Limiting**
- **Built-in**: 5 login attempts per 15 minutes per IP/email
- **Production**: Consider Redis-based rate limiting for distributed deployments

---

## **📊 Monitoring & Health Checks**

### **Health Check Endpoint**
```bash
# Check application health
curl https://your-domain.com/api/health

# Expected response
{
  "status": "healthy",
  "timestamp": "2024-05-30T00:47:01.000Z",
  "services": {
    "database": "healthy",
    "auth": "healthy"
  },
  "version": "0.0.1"
}
```

### **Logging**
```bash
# Production logs (structured JSON)
LOG_LEVEL=error          # Only log errors
DISABLE_DEBUG_LOGS=true  # No debug information

# Log levels: debug, info, warn, error
```

### **Monitoring Setup**
```bash
# Application Performance Monitoring (APM)
# - Sentry for error tracking
# - New Relic for performance
# - DataDog for infrastructure

# Database Monitoring
# - Check disk space (SQLite)
# - Monitor connection pool (PostgreSQL)
# - Set up automated backups
```

---

## **🔄 CI/CD Pipeline Example**

### **GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Build application
        run: npm run build:node
        
      - name: Deploy to server
        run: |
          # Your deployment script
          scp -r build/ user@server:/app/
          ssh user@server "cd /app && npm run db:migrate && pm2 restart petagotchi-api"
```

---

## **🚨 Troubleshooting**

### **Common Production Issues**

**Database Connection Errors**
```bash
# Check database file permissions (SQLite)
ls -la prisma/production.db
chmod 644 prisma/production.db

# Check PostgreSQL connection
psql $DATABASE_URL -c "SELECT 1;"
```

**Build Failures**
```bash
# Clear build cache
rm -rf .svelte-kit build-node build-static

# Check Node.js version
node --version  # Should be 22+

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

**CORS Issues**
```bash
# Check allowed origins
echo $PUBLIC_ALLOWED_ORIGINS

# Verify mobile app can reach API
curl -H "Origin: capacitor://localhost" https://your-api.com/api/health
```

### **Performance Optimization**
```bash
# Enable gzip compression (nginx/apache)
# Set up CDN for static assets
# Configure database connection pooling
# Implement Redis caching (optional)
```

---

## **📋 Production Checklist**

### **Pre-Deployment**
- [ ] Environment variables configured
- [ ] Database migrations tested
- [ ] HTTPS certificates installed
- [ ] CORS origins configured
- [ ] Rate limiting tested
- [ ] Health checks working
- [ ] Backup strategy implemented

### **Post-Deployment**
- [ ] Health check returns 200
- [ ] User registration/login works
- [ ] Mobile app connects to API
- [ ] Logs are being generated
- [ ] Monitoring alerts configured
- [ ] Performance baseline established

### **Ongoing Maintenance**
- [ ] Regular database backups
- [ ] Security updates applied
- [ ] Performance monitoring
- [ ] Log rotation configured
- [ ] Capacity planning reviewed
