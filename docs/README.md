# 🐾 Petagotchi Documentation

<div align="center">

**A modern full-stack application with dual deployment architecture**

[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![SvelteKit](https://img.shields.io/badge/SvelteKit-FF3E00?style=for-the-badge&logo=svelte&logoColor=white)](https://kit.svelte.dev/)
[![Prisma](https://img.shields.io/badge/Prisma-3982CE?style=for-the-badge&logo=Prisma&logoColor=white)](https://prisma.io/)
[![SQLite](https://img.shields.io/badge/SQLite-07405E?style=for-the-badge&logo=sqlite&logoColor=white)](https://sqlite.org/)

</div>

---

## 🚀 **Quick Navigation**

<table>
<tr>
<td width="50%">

### 🛠️ **For Developers**

- **[Development Setup →](./development.md)**
- **[Architecture Deep Dive →](./architecture.md)**
- **[Authentication System →](./auth.md)**

</td>
<td width="50%">

### 🚀 **For Deployment**

- **[Production Guide →](./production.md)**
- **[Environment Config →](./production.md#environment-configuration)**
- **[Security Checklist →](./production.md#security-configuration)**

</td>
</tr>
</table>

---

## 🏗️ **Architecture at a Glance**

```mermaid
graph TB
    subgraph "Client Applications"
        WEB[🌐 Web Browser]
        MOBILE[📱 Android App]
    end

    subgraph "Dual Build System"
        NODE[🟢 Node.js Build<br/>build-node/]
        STATIC[📦 Static Build<br/>build-static/]
    end

    subgraph "Backend Services"
        API[🔌 API Server<br/>SvelteKit + Node.js]
        AUTH[🔐 Authentication<br/>Argon2 + Sessions]
        DB[(🗄️ Database<br/>SQLite/PostgreSQL)]
    end

    WEB --> NODE
    MOBILE --> STATIC
    STATIC -.->|API Calls| API
    NODE --> API
    API --> AUTH
    API --> DB
    AUTH --> DB
```

> **🎯 Key Innovation**: One codebase, two deployment targets - web server and mobile app

---

## 📚 **Documentation Hub**

<div align="center">

| 📖 **Guide**                             | 🎯 **Purpose**                  | 🔑 **Key Topics**                                          |
| :--------------------------------------- | :------------------------------ | :--------------------------------------------------------- |
| **[🛠️ Development](./development.md)**   | Complete local setup & workflow | Setup • Database • Commands • Mobile • Testing • Debugging |
| **[🚀 Production](./production.md)**     | Deployment & operations guide   | Build • Deploy • Security • Monitoring • CI/CD • Docker    |
| **[🏗️ Architecture](./architecture.md)** | System design deep dive         | Dual Adapters • Database • Auth Flow • API • Performance   |
| **[🔐 Authentication](./auth.md)**       | Security implementation details | Argon2 • Sessions • Rate Limiting • Validation • CORS      |

</div>

> 💡 **Pro Tip**: Start with [Development Guide](./development.md) for setup, then explore [Architecture](./architecture.md) for understanding the system design.

---

## ⚡ **Quick Start**

<table>
<tr>
<td width="50%">

### 🛠️ **Development Setup**

```bash
# 1️⃣ Setup project
cd src/web && npm install

# 2️⃣ Configure environment
cp .env.example .env

# 3️⃣ Initialize database
npm run db:generate
npm run db:migrate:dev

# 4️⃣ Start development
npm run dev
```

**→ Open [http://localhost:5173](http://localhost:5173)**

</td>
<td width="50%">

### 🚀 **Production Deploy**

```bash
# 1️⃣ Build application
npm run build:node

# 2️⃣ Run migrations
npm run db:migrate

# 3️⃣ Start server
npm run start:prod
```

**→ Check [/api/health](http://localhost:3000/api/health)**

</td>
</tr>
</table>

<div align="center">

**📱 Mobile Development**: `npm run android` • **🔍 Database**: `npm run db:studio` • **🧪 Testing**: `npm test`

</div>

---

## 🛠️ **Technology Stack**

<table>
<tr>
<td width="33%">

### 🎨 **Frontend**

- **Framework**: SvelteKit 2.x
- **Language**: TypeScript
- **Styling**: TailwindCSS
- **Mobile**: Capacitor
- **Build**: Vite

</td>
<td width="33%">

### ⚙️ **Backend**

- **Runtime**: Node.js 22+
- **Database**: SQLite/PostgreSQL
- **ORM**: Prisma
- **Auth**: Argon2 + Sessions
- **Validation**: Zod

</td>
<td width="33%">

### 🔐 **Security**

- **Hashing**: Argon2id
- **Rate Limiting**: 5/15min
- **Validation**: Zod schemas
- **Sessions**: HttpOnly + Secure
- **CORS**: Origin validation

</td>
</tr>
</table>

<div align="center">

**🏗️ Architecture**: [Dual Adapter System](./architecture.md#dual-adapter-strategy) • **📊 Performance**: [Optimization Guide](./architecture.md#performance-considerations)

</div>

---

## 📋 **Essential Commands**

<div align="center">

| 🎯 **Task**      | 💻 **Command**         | 📖 **Learn More**                                                   |
| :--------------- | :--------------------- | :------------------------------------------------------------------ |
| **Development**  | `npm run dev`          | [Development Guide →](./development.md#development-commands)        |
| **Build Web**    | `npm run build:node`   | [Production Guide →](./production.md#build-process)                 |
| **Build Mobile** | `npm run build:static` | [Mobile Development →](./development.md#mobile-development-android) |
| **Database**     | `npm run db:studio`    | [Database Setup →](./development.md#database-setup)                 |
| **Health Check** | `curl /api/health`     | [Monitoring →](./production.md#monitoring--health-checks)           |

</div>

---

## 🔗 **Key Resources**

<table>
<tr>
<td width="50%">

### 📊 **API & Database**

- **[API Endpoints →](./auth.md#api-endpoints)**
- **[Database Schema →](./development.md#database-schema)**
- **[Environment Config →](./production.md#environment-configuration)**

</td>
<td width="50%">

### 🛠️ **Development & Deploy**

- **[Local Setup →](./development.md#quick-start)**
- **[Build Process →](./production.md#production-build-process)**
- **[Troubleshooting →](./development.md#debugging--troubleshooting)**

</td>
</tr>
</table>

---

## 🎯 **What's Next?**

<div align="center">

### 🚀 **Choose Your Path**

<table>
<tr>
<td width="50%">

#### 👨‍💻 **I'm a Developer**

1. **[🛠️ Development Setup →](./development.md#quick-start)**
2. **[🏗️ Architecture Overview →](./architecture.md#system-overview)**
3. **[🔐 Auth Implementation →](./auth.md)**

</td>
<td width="50%">

#### 🚀 **I'm Deploying**

1. **[📋 Production Checklist →](./production.md#production-checklist)**
2. **[🔧 Environment Config →](./production.md#environment-configuration)**
3. **[📊 Monitoring Setup →](./production.md#monitoring--health-checks)**

</td>
</tr>
</table>

</div>

---

## 🆘 **Need Help?**

<div align="center">

| 🔍 **Issue**       | 🛠️ **Solution**                                                       | 📖 **Guide**                            |
| :----------------- | :-------------------------------------------------------------------- | :-------------------------------------- |
| **Setup Problems** | Check [troubleshooting](./development.md#debugging--troubleshooting)  | [Development Guide](./development.md)   |
| **Build Errors**   | Review [common issues](./production.md#troubleshooting)               | [Production Guide](./production.md)     |
| **Auth Issues**    | Verify [security config](./auth.md)                                   | [Authentication Guide](./auth.md)       |
| **Performance**    | See [optimization tips](./architecture.md#performance-considerations) | [Architecture Guide](./architecture.md) |

**🏥 Health Check**: `curl http://localhost:3000/api/health` • **🔍 Database**: `npm run db:studio` • **📊 Logs**: Check browser console

</div>

---

<div align="center">

**🐾 Built with ❤️ for the Petagotchi project**

_For detailed guides, explore the documentation links above_

</div>
