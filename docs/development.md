# 🛠️ Development Guide

## **Architecture Overview**

The application uses a **dual adapter architecture**:

- **Node.js Adapter** (`build-node/`) - Web application with API endpoints
- **Static Adapter** (`build-static/`) - SPA for Android/Capacitor apps

The static build consumes APIs from the Node.js backend, enabling both web and mobile deployment from the same codebase.

---

## **🚀 Quick Start**

### **Prerequisites**

- Node.js 22+ (required for @node-rs/argon2)
- npm or yarn
- Git

### **Initial Setup**

```bash
# Clone and navigate to project
cd src/web

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your local settings

# Initialize database
npm run db:generate
npm run db:migrate:dev

# Start development server
npm run dev
```

---

## **🗄️ Database Setup**

### **Technology Stack**

- **Database**: SQLite (development) / PostgreSQL (production recommended)
- **ORM**: Prisma
- **Migrations**: Prisma Migrate
- **Authentication**: Argon2 password hashing + session-based auth

### **Development Database**

```bash
# Generate Prisma client
npm run db:generate

# Create and apply migrations
npm run db:migrate:dev

# View database in browser
npm run db:studio

# Reset database (if needed)
npx prisma migrate reset
```

### **Database Schema**

```sql
-- Users table
CREATE TABLE User (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user',
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table
CREATE TABLE Session (
    id TEXT PRIMARY KEY,
    userId TEXT NOT NULL,
    expiresAt DATETIME NOT NULL,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (userId) REFERENCES User(id) ON DELETE CASCADE
);
```

---

## **🔧 Development Commands**

### **Server Development**

```bash
# Start Node.js development server (API + Web)
npm run dev:node          # http://localhost:5173

# Start static development server (Android/SPA)
npm run dev:static        # http://localhost:5173

# Default development (uses Node.js adapter)
npm run dev
```

### **Database Commands**

```bash
npm run db:generate       # Generate Prisma client
npm run db:migrate:dev    # Create and apply migration
npm run db:studio         # Open database browser
npx prisma db push        # Push schema changes (dev only)
npx prisma db seed        # Run seed script (if configured)
```

### **Code Quality**

```bash
npm run lint              # ESLint + Prettier check
npm run format            # Format code with Prettier
npm run check             # TypeScript + Svelte check
npm run check:watch       # Watch mode for type checking
```

### **Testing**

```bash
npm run test              # Run all tests
npm run test:unit         # Unit tests with Vitest
npm run test:e2e          # E2E tests with Playwright
```

---

## **📱 Mobile Development (Android)**

### **Capacitor Setup**

```bash
# Build static version and sync with Capacitor
npm run cap:sync

# Open Android Studio
npm run cap:open android

# Build Android app
npm run cap:build

# Combined: build + sync + open
npm run android
```

### **Development Workflow**

1. **Web Development**: Use `npm run dev:static` for SPA development
2. **API Testing**: Static app consumes APIs from Node.js server
3. **Mobile Testing**: Use `npm run android` to test in Android Studio
4. **Live Reload**: Capacitor supports live reload during development

---

## **🔐 Authentication Development**

### **Current Implementation**

- **Password Hashing**: Argon2id with secure defaults
- **Session Management**: Database-stored sessions with expiration
- **Rate Limiting**: 5 attempts per 15 minutes per IP/email
- **Input Validation**: Zod schemas with sanitization
- **UI Design**: shadcn-svelte login-03 block with modern styling

### **API Endpoints**

```bash
POST /api/auth/register    # User registration
POST /api/auth/login       # User login
POST /api/auth/logout      # User logout
GET  /api/auth/me          # Get current user
GET  /api/health           # Health check
```

### **Testing Authentication**

```bash
# Register new user
curl -X POST http://localhost:5173/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"Test123!"}'

# Login
curl -X POST http://localhost:5173/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

---

## **🌍 Environment Configuration**

### **Development (.env)**

```bash
# Public variables (client-side)
PUBLIC_API_BASE=/api
PUBLIC_API_URL=http://localhost:5173
PUBLIC_ALLOWED_ORIGINS=http://localhost:5173,capacitor://localhost

# Private variables (server-side)
DATABASE_URL="file:./prisma/dev.db"
NODE_ENV=development
```

### **Environment Variables**

- **PUBLIC\_\*** - Available on client-side
- **DATABASE_URL** - Prisma database connection
- **PUBLIC_ALLOWED_ORIGINS** - CORS allowed origins (comma-separated)
- **NODE_ENV** - Environment mode (development/production)

---

## **🔍 Debugging & Troubleshooting**

### **Common Issues**

**Database Connection Issues**

```bash
# Check database file exists
ls -la prisma/dev.db

# Regenerate Prisma client
npm run db:generate

# Reset database
npx prisma migrate reset
```

**CORS Issues**

- Check `PUBLIC_ALLOWED_ORIGINS` includes your development URL
- Verify `capacitor://localhost` is included for mobile development

**Build Issues**

```bash
# Clear build cache
rm -rf .svelte-kit build-node build-static

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### **Development Tools**

- **Database**: `npm run db:studio` - Visual database browser
- **API Testing**: Use Postman, curl, or browser dev tools
- **Logs**: Check browser console and terminal output
- **Type Checking**: `npm run check:watch` for real-time type checking

---

## **📁 Project Structure**

```
src/web/
├── src/
│   ├── lib/
│   │   ├── domain/auth/          # Authentication logic
│   │   ├── infrastructure/db/    # Database configuration
│   │   ├── schemas/             # Zod validation schemas
│   │   └── utils/               # Utilities (logger, rate-limiter)
│   ├── routes/
│   │   ├── api/                 # API endpoints
│   │   └── (app)/               # Web application routes
│   └── app.html                 # HTML template
├── prisma/
│   ├── schema.prisma            # Database schema
│   ├── migrations/              # Database migrations
│   └── dev.db                   # SQLite database (dev)
├── build-node/                  # Node.js build output
├── build-static/                # Static build output
└── docs/                        # Documentation
```
