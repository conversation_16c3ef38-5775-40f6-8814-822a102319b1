{"name": "web", "private": true, "version": "0.0.1", "type": "module", "engines": {"node": ">=22.12.0", "npm": ">=9.8.1"}, "scripts": {"dev": "cross-env NODE_ENV=development npm run dev:node", "dev:node": "cross-env NODE_ENV=development VITE_ADAPTER=node vite dev --port 5173", "build": "prisma generate && vite build", "build:static": "cross-env NODE_ENV=production VITE_ADAPTER=static vite build && npm run db:generate", "build:netlify-adapter": "prisma generate && vite build", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "prepare": "svelte-kit sync || echo ''", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "preview": "cross-env NODE_ENV=production npm run build:static && VITE_ADAPTER=static vite preview", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "test:unit": "vitest", "capacitor:copy:before": "cross-env NODE_ENV=production npm run build:static", "capacitor": "capacitor", "cap:sync": "npm run capacitor:copy:before && npx cap sync", "cap:build": "npm run capacitor:copy:before && npx cap build", "cap:open": "npm run capacitor:copy:before && npx cap open", "android": "npm run cap:sync && npm run cap:open android", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "start": "node build/index.js", "start:prod": "NODE_ENV=production node build/index.js"}, "devDependencies": {"@capacitor/android": "^6.2.0", "@capacitor/cli": "^6.2.0", "@capacitor/core": "^6.2.0", "@capacitor/ios": "^6.2.0", "@dnd-kit-svelte/core": "^0.0.8", "@dnd-kit-svelte/modifiers": "^0.0.8", "@dnd-kit-svelte/sortable": "^0.0.8", "@dnd-kit-svelte/utilities": "^0.0.8", "@eslint/compat": "^1.2.3", "@inlang/paraglide-js": "^2.0.13", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.482.0", "@netlify/functions": "^4.1.12", "@playwright/test": "^1.49.1", "@sveltejs/adapter-netlify": "^5.1.0", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tabler/icons-svelte": "^3.33.0", "@tanstack/table-core": "^8.21.3", "@types/d3-scale": "^4.0.9", "@types/d3-shape": "^3.1.7", "@types/google.maps": "^3.58.1", "@types/node": "^22.16.5", "autoprefixer": "^10.4.20", "bits-ui": "^2.3.0", "clsx": "^2.1.1", "cross-env": "^7.0.3", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.46.0", "formsnap": "^2.0.1", "globals": "^15.0.0", "layerchart": "^2.0.0-next.10", "lucide-svelte": "^0.469.0", "mode-watcher": "^1.0.7", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.15.0", "svelte-check": "^4.0.8", "svelte-feather-icons": "^4.2.0", "svelte-sonner": "^1.0.3", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tw-animate-css": "^1.3.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "vaul-svelte": "^1.0.0-next.7", "vite": "^5.4.11", "vitest": "^2.1.8", "zod": "^3.25.36"}, "dependencies": {"@azure/storage-blob": "^12.27.0", "@libsql/client": "^0.9.0", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@prisma/client": "^6.8.2", "@tailwindcss/vite": "^4.1.8", "@types/dotenv": "^6.1.1", "@types/lodash": "^4.17.17", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "argon2": "^0.43.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "lodash": "^4.17.21", "pg": "^8.16.3", "prisma": "^6.8.2", "svelte-lexical": "^0.5.3", "svelte-maplibre-gl": "^0.1.8", "tailwindcss": "^4.1.8", "tsx": "^4.19.4", "uuid": "^11.1.0"}}