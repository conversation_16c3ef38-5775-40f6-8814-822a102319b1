import { PrismaClient } from '@prisma/client';
import { hash } from 'argon2';

const prisma = new PrismaClient();

async function main() {
	console.log('🌱 Starting database seed...');

	// Clear existing data (optional - comment out if you want to keep existing data)
	console.log('🧹 Clearing existing data...');
	await prisma.payment.deleteMany();
	await prisma.booking.deleteMany();
	await prisma.enrollment.deleteMany();
	await prisma.dog.deleteMany();
	await prisma.trainingProgramStep.deleteMany();
	await prisma.trainingProgram.deleteMany();
	await prisma.trainingLocation.deleteMany();
	await prisma.course.deleteMany();
	await prisma.dogSchoolMember.deleteMany();
	await prisma.address.deleteMany();
	await prisma.dogSchool.deleteMany();
	await prisma.session.deleteMany();
	await prisma.user.deleteMany();
	await prisma.publicTrainingSession.deleteMany();
	await prisma.publicTrainingLocation.deleteMany();

	console.log('👥 Creating users...');

	// Create users
	const adminUser = await prisma.user.create({
		data: {
			email: '<EMAIL>',
			name: 'Admin User',
			password_hash: await hash('admin123'),
			role: 'admin'
		}
	});

	const trainerUser = await prisma.user.create({
		data: {
			email: '<EMAIL>',
			name: 'John Trainer',
			password_hash: await hash('trainer123'),
			role: 'trainer'
		}
	});

	const regularUser = await prisma.user.create({
		data: {
			email: '<EMAIL>',
			name: 'Jane User',
			password_hash: await hash('user123'),
			role: 'user'
		}
	});

	console.log('🏫 Creating dog schools...');

	// Create addresses
	const address1 = await prisma.address.create({
		data: {
			street: '123 Main Street',
			postalCode: '8001',
			city: 'Zurich',
			state: 'ZH',
			country: 'Switzerland',
			latitude: 47.3769,
			longitude: 8.5417
		}
	});

	const address2 = await prisma.address.create({
		data: {
			street: '456 Park Avenue',
			postalCode: '8002',
			city: 'Zurich',
			state: 'ZH',
			country: 'Switzerland',
			latitude: 47.3782,
			longitude: 8.5395
		}
	});

	// Create dog schools
	const dogSchool1 = await prisma.dogSchool.create({
		data: {
			name: 'Happy Paws Training',
			description: 'Professional dog training services in Zurich',
			slug: 'happy-paws-training',
			mobile: '+41 44 123 4567',
			isActive: true,
			addressId: address1.id
		}
	});

	const dogSchool2 = await prisma.dogSchool.create({
		data: {
			name: 'Canine Academy',
			description: 'Advanced dog training and behavior modification',
			slug: 'canine-academy',
			mobile: '+41 44 987 6543',
			isActive: true,
			addressId: address2.id
		}
	});

	console.log('👥 Creating dog school memberships...');

	// Create dog school memberships
	await prisma.dogSchoolMember.create({
		data: {
			userId: trainerUser.id,
			dogSchoolId: dogSchool1.id,
			role: 'owner'
		}
	});

	await prisma.dogSchoolMember.create({
		data: {
			userId: regularUser.id,
			dogSchoolId: dogSchool1.id,
			role: 'client'
		}
	});

	console.log('🏃 Creating training programs...');

	// Create training programs
	const basicTraining = await prisma.trainingProgram.create({
		data: {
			name: 'Basic Obedience Training',
			description: 'Learn fundamental commands like sit, stay, come, and heel',
			dogSchoolId: dogSchool1.id,
			status: 'published',
			image: 'https://images.unsplash.com/photo-1587300003388-59208cc962cb?w=400'
		}
	});

	const advancedTraining = await prisma.trainingProgram.create({
		data: {
			name: 'Advanced Agility Course',
			description: 'Master complex agility obstacles and improve coordination',
			dogSchoolId: dogSchool1.id,
			status: 'published',
			image: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400'
		}
	});

	console.log('📝 Creating training program steps...');

	// Create training program steps
	await prisma.trainingProgramStep.createMany({
		data: [
			{
				programId: basicTraining.id,
				title: 'Sit Command',
				description: 'Teach your dog to sit on command',
				position: 1,
				type: 'command',
				durationMinutes: 15,
				status: 'published'
			},
			{
				programId: basicTraining.id,
				title: 'Stay Command',
				description: 'Teach your dog to stay in position',
				position: 2,
				type: 'command',
				durationMinutes: 20,
				status: 'published'
			},
			{
				programId: basicTraining.id,
				title: 'Come Command',
				description: 'Teach your dog to come when called',
				position: 3,
				type: 'command',
				durationMinutes: 25,
				status: 'published'
			},
			{
				programId: advancedTraining.id,
				title: 'Weave Poles',
				description: 'Navigate through weave poles with precision',
				position: 1,
				type: 'agility',
				durationMinutes: 30,
				status: 'published'
			},
			{
				programId: advancedTraining.id,
				title: 'A-Frame',
				description: 'Climb and descend the A-frame obstacle',
				position: 2,
				type: 'agility',
				durationMinutes: 35,
				status: 'published'
			}
		]
	});

	console.log('🏟️ Creating training locations...');

	// Create training locations
	await prisma.trainingLocation.create({
		data: {
			name: 'Main Training Field',
			description: 'Large outdoor training area with various obstacles',
			type: 'OUTDOOR_PRIVATE',
			dogSchoolId: dogSchool1.id,
			addressId: address1.id
		}
	});

	await prisma.trainingLocation.create({
		data: {
			name: 'Indoor Training Center',
			description: 'Climate-controlled indoor facility for year-round training',
			type: 'INDOOR',
			dogSchoolId: dogSchool1.id,
			addressId: address2.id
		}
	});

	console.log('🐕 Creating dogs...');

	// Create dogs
	const dog1 = await prisma.dog.create({
		data: {
			name: 'Buddy',
			breed: 'Golden Retriever',
			birthDate: new Date('2021-01-15'),
			ownerId: regularUser.id
		}
	});

	const dog2 = await prisma.dog.create({
		data: {
			name: 'Max',
			breed: 'German Shepherd',
			birthDate: new Date('2022-03-20'),
			ownerId: regularUser.id
		}
	});

	console.log('📚 Creating courses...');

	// Create courses
	const course1 = await prisma.course.create({
		data: {
			title: 'Puppy Training Basics',
			dogSchoolId: dogSchool1.id,
			trainingProgramId: basicTraining.id,
			locationId: (await prisma.trainingLocation.findFirst({
				where: { dogSchoolId: dogSchool1.id }
			}))!.id,
			startDate: new Date('2024-02-01'),
			endDate: new Date('2024-03-01'),
			capacity: 10
		}
	});

	console.log('📝 Creating enrollments...');

	// Create enrollments
	const enrollment1 = await prisma.enrollment.create({
		data: {
			userId: regularUser.id,
			courseId: course1.id,
			dogId: dog1.id,
			status: 'CONFIRMED'
		}
	});

	console.log('📅 Creating bookings...');

	// Create bookings
	const booking1 = await prisma.booking.create({
		data: {
			userId: regularUser.id,
			enrollmentId: enrollment1.id,
			confirmed: true,
			comment: 'First session - basic obedience'
		}
	});

	console.log('💰 Creating payments...');

	// Create payments
	await prisma.payment.create({
		data: {
			bookingId: booking1.id,
			amount: 299.99,
			status: 'COMPLETED',
			method: 'CREDIT_CARD',
			transactionId: 'txn_123456789'
		}
	});

	console.log('📍 Creating public training locations...');

	// Create public training locations
	await prisma.publicTrainingLocation.create({
		data: {
			name: 'Zurich Dog Park',
			address: 'Utoquai 1, 8008 Zürich',
			description: 'Popular dog park with training areas',
			website: 'https://www.stadt-zuerich.ch',
			image: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400',
			lat: 47.3569,
			lng: 8.5417
		}
	});

	console.log('🎯 Creating public training sessions...');

	// Create public training sessions
	await prisma.publicTrainingSession.create({
		data: {
			title: 'Free Obedience Workshop',
			trainer: 'John Trainer',
			location: 'Zurich Dog Park',
			image: 'https://images.unsplash.com/photo-1587300003388-59208cc962cb?w=400',
			lat: 47.3569,
			lng: 8.5417,
			description: 'Free workshop for basic obedience training'
		}
	});

	console.log('✅ Database seed completed successfully!');
	console.log('\n📊 Summary:');
	console.log(`- Users: 3 (admin, trainer, regular user)`);
	console.log(`- Dog Schools: 2`);
	console.log(`- Training Programs: 2`);
	console.log(`- Training Program Steps: 5`);
	console.log(`- Training Locations: 2`);
	console.log(`- Dogs: 2`);
	console.log(`- Courses: 1`);
	console.log(`- Enrollments: 1`);
	console.log(`- Bookings: 1`);
	console.log(`- Payments: 1`);
	console.log(`- Public Locations: 1`);
	console.log(`- Public Sessions: 1`);

	console.log('\n🔑 Test Accounts:');
	console.log('Admin: <EMAIL> / admin123');
	console.log('Trainer: <EMAIL> / trainer123');
	console.log('User: <EMAIL> / user123');
}

main()
	.catch((e) => {
		console.error('❌ Error seeding database:', e);
		process.exit(1);
	})
	.finally(async () => {
		await prisma.$disconnect();
	});
