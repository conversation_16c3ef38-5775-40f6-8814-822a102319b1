// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

enum UserRole {
  user
  trainer
  admin
}

enum DogSchoolMemberRole {
  owner
  trainer
  client
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum PaymentMethod {
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  STRIPE
  CASH
  INVOICE
  TWINT
}

enum EnrollmentStatus {
  PENDING
  CONFIRMED
  CANCELLED
  WAITLISTED
}

model User {
  id           String    @id @default(uuid())
  email        String    @unique
  name         String
  password_hash String
  role         UserRole  @default(user)
  sessions     Session[]
  
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  dogs      Dog[]
  memberships DogSchoolMember[]
  enrollments Enrollment[]
  bookings  Booking[]
}

model Session {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model PublicTrainingLocation {
  id          String   @id @default(uuid())
  name        String
  address     String
  description String?
  website     String?
  image       String?
  lat         Float
  lng         Float
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("public_training_location")
}

model PublicTrainingSession {
  id          String   @id @default(uuid())
  title       String
  trainer     String
  location    String
  image       String
  lat         Float
  lng         Float
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("public_training_session")
}

model TrainingProgram {
  id           String              @id @default(uuid())
  name         String
  description  String?
  dogSchoolId  String?             // Make it optional temporarily
  dogSchool    DogSchool?          @relation(fields: [dogSchoolId], references: [id])
  steps        TrainingProgramStep[]
  status       String             @default("draft")
  image        String?
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt
  courses      Course[]
}

model TrainingProgramStep {
  id             Int              @id @default(autoincrement())
  programId      String
  program        TrainingProgram  @relation(fields: [programId], references: [id], onDelete: Cascade)
  parentId       Int?
  parent         TrainingProgramStep? @relation("StepHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children       TrainingProgramStep[] @relation("StepHierarchy")
  title          String
  description    String?
  position       Int
  type           String
  durationMinutes Int?
  image          String?
  links          String           @default("[]")
  status         String           @default("draft")
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
}

model DogSchool {
  id          String   @id @default(uuid())
  name        String
  description String?
  logo        String?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  slug        String   @unique         // For URL use (e.g. /schools/happy-paws)
  logoUrl     String?                 // URL to logo image
  
  mobile       String?                 // Public contact phone
  isActive    Boolean  @default(true) // Soft disable a school

    // Team members relation, depending on ther role
  members     DogSchoolMember[]

  address     Address?   @relation(fields: [addressId], references: [id])
  addressId   String?    @unique

  trainingLocations TrainingLocation[]
  trainingPrograms  TrainingProgram[]
  courses           Course[]
}

model DogSchoolMember {
  id           String               @id @default(uuid())
  user         User                 @relation(fields: [userId], references: [id])
  userId       String
  dogSchool    DogSchool            @relation(fields: [dogSchoolId], references: [id])
  dogSchoolId  String
  role         DogSchoolMemberRole 
  createdAt    DateTime             @default(now())

  @@unique([userId, dogSchoolId])
}

model Address {
  id         String   @id @default(uuid())
  street     String
  street2    String?
  postalCode String
  city       String
  state      String?
  country    String
  latitude   Float?
  longitude  Float?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // reverse relations
  dogSchool        DogSchool?
  trainingLocation TrainingLocation?
}

model TrainingLocation {
  id           String      @id @default(uuid())
  name         String                         // e.g., "Forest Trail South", "Main Training Field"
  description  String?                        // Optional notes about the place
  type         String                   // Enum: INDOOR, OUTDOOR_PUBLIC, OUTDOOR_PRIVATE
  address     Address?   @relation(fields: [addressId], references: [id])
  addressId   String?    @unique

  dogSchool    DogSchool   @relation(fields: [dogSchoolId], references: [id])
  dogSchoolId  String

  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  courses      Course[]                       // Courses scheduled at this location
}

model Course {
  id            String           @id @default(uuid())
  title         String
  startDate     DateTime
  endDate       DateTime
  capacity      Int
  dogSchool     DogSchool        @relation(fields: [dogSchoolId], references: [id])
  dogSchoolId   String
  trainingProgramId     String
  trainingProgram       TrainingProgram  @relation(fields: [trainingProgramId], references: [id])
  location      TrainingLocation @relation(fields: [locationId], references: [id])
  locationId    String
  enrollments   Enrollment[]
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
}


model Dog {
  id        String     @id @default(uuid())
  name      String
  breed     String?
  birthDate DateTime?
  owner     User       @relation(fields: [ownerId], references: [id])
  ownerId   String
  enrollments Enrollment[]
  createdAt DateTime   @default(now())
  updatedAt DateTime @updatedAt
}

model Enrollment {
  id         String     @id @default(uuid())
  user       User       @relation(fields: [userId], references: [id])
  userId     String
  course     Course     @relation(fields: [courseId], references: [id])
  courseId   String
  dog        Dog        @relation(fields: [dogId], references: [id])
  dogId      String
  status     EnrollmentStatus
  comment    String?
  booking    Booking?
 
  createdAt  DateTime   @default(now())
  updatedAt  DateTime @updatedAt
}

model Booking {
  id           String     @id @default(uuid())
  enrollment   Enrollment @relation(fields: [enrollmentId], references: [id])
  enrollmentId String     @unique
  user         User       @relation(fields: [userId], references: [id])
  userId       String
  bookedAt     DateTime   @default(now())
  confirmed    Boolean    @default(false)
  comment      String?
  payments     Payment[]
}

model Payment {
  id            String        @id @default(uuid())
  booking       Booking       @relation(fields: [bookingId], references: [id])
  bookingId     String
  amount        Decimal
  status        PaymentStatus
  method        PaymentMethod
  transactionId String?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
}
