import type { UserRole } from './types';

/**
 * Role-based permission system for the dog training app
 */

// Define permissions
export const PERMISSIONS = {
	// Content management
	CREATE_COURSE: 'create_course',
	EDIT_COURSE: 'edit_course',
	DELETE_COURSE: 'delete_course',
	PUBLISH_COURSE: 'publish_course',

	// Booking management
	VIEW_BOOKINGS: 'view_bookings',
	MANAGE_BOOKINGS: 'manage_bookings',
	CANCEL_BOOKING: 'cancel_booking',

	// User management
	VIEW_USERS: 'view_users',
	EDIT_USERS: 'edit_users',
	DELETE_USERS: 'delete_users',

	// Training locations
	CREATE_LOCATION: 'create_location',
	EDIT_LOCATION: 'edit_location',
	DELETE_LOCATION: 'delete_location',

	// Analytics and reports
	VIEW_ANALYTICS: 'view_analytics',
	VIEW_REPORTS: 'view_reports',

	// Platform administration
	MODERATE_CONTENT: 'moderate_content',
	<PERSON>NA<PERSON>_PLATFORM: 'manage_platform'
} as const;

export type Permission = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];

// Role-permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
	user: [
		PERMISSIONS.CANCEL_BOOKING // Users can cancel their own bookings
	],
	trainer: [
		// Content management
		PERMISSIONS.CREATE_COURSE,
		PERMISSIONS.EDIT_COURSE,
		PERMISSIONS.DELETE_COURSE,
		PERMISSIONS.PUBLISH_COURSE,

		// Booking management for their courses
		PERMISSIONS.VIEW_BOOKINGS,
		PERMISSIONS.MANAGE_BOOKINGS,

		// Location management for their locations
		PERMISSIONS.CREATE_LOCATION,
		PERMISSIONS.EDIT_LOCATION,

		// Analytics for their content
		PERMISSIONS.VIEW_ANALYTICS,
		PERMISSIONS.VIEW_REPORTS,

		// User permissions
		PERMISSIONS.CANCEL_BOOKING
	],
	admin: [
		// All permissions
		...Object.values(PERMISSIONS)
	]
};

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(role: UserRole, permission: Permission): boolean {
	return ROLE_PERMISSIONS[role].includes(permission);
}

/**
 * Check if a user role has any of the specified permissions
 */
export function hasAnyPermission(role: UserRole, permissions: Permission[]): boolean {
	return permissions.some((permission) => hasPermission(role, permission));
}

/**
 * Check if a user role has all of the specified permissions
 */
export function hasAllPermissions(role: UserRole, permissions: Permission[]): boolean {
	return permissions.every((permission) => hasPermission(role, permission));
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: UserRole): Permission[] {
	return ROLE_PERMISSIONS[role];
}

/**
 * Role hierarchy for privilege escalation checks
 */
export const ROLE_HIERARCHY: Record<UserRole, number> = {
	user: 1,
	trainer: 2,
	admin: 3
};

/**
 * Check if one role has higher privileges than another
 */
export function hasHigherPrivileges(role1: UserRole, role2: UserRole): boolean {
	return ROLE_HIERARCHY[role1] > ROLE_HIERARCHY[role2];
}

/**
 * Check if a role can manage another role
 */
export function canManageRole(managerRole: UserRole): boolean {
	// Admins can manage everyone
	if (managerRole === 'admin') return true;

	// Trainers cannot manage other roles
	if (managerRole === 'trainer') return false;

	// Users cannot manage anyone
	return false;
}

/**
 * Route-based permission checks for different app sections
 */
export const ROUTE_PERMISSIONS: Record<string, Permission[]> = {
	'/app/admin': [PERMISSIONS.MANAGE_PLATFORM],
	'/app/trainer': [PERMISSIONS.CREATE_COURSE, PERMISSIONS.VIEW_ANALYTICS],
	'/app/courses/create': [PERMISSIONS.CREATE_COURSE],
	'/app/courses/edit': [PERMISSIONS.EDIT_COURSE],
	'/app/locations/create': [PERMISSIONS.CREATE_LOCATION],
	'/app/analytics': [PERMISSIONS.VIEW_ANALYTICS],
	'/app/users': [PERMISSIONS.VIEW_USERS]
};

/**
 * Check if a role can access a specific route
 */
export function canAccessRoute(role: UserRole, route: string): boolean {
	const requiredPermissions = ROUTE_PERMISSIONS[route];
	if (!requiredPermissions) return true; // No specific permissions required

	return hasAnyPermission(role, requiredPermissions);
}

/**
 * STRICT Dashboard Route Access Control
 * Users can ONLY access their own role-specific dashboard routes
 */
export function hasStrictRoutePermission(userRole: UserRole, route: string): boolean {
	// Remove leading slash and split into segments
	const segments = route.replace(/^\//, '').split('/');

	// Must start with 'dashboard'
	if (segments[0] !== 'dashboard') {
		return true; // Allow non-dashboard routes
	}

	// If it's just /dashboard, always allow (it redirects based on role)
	if (segments.length === 1) {
		return true;
	}

	const roleSegment = segments[1];

	// Strict role-based access control - users can ONLY access their own role routes
	switch (userRole) {
		case 'admin':
			// Admin can only access /dashboard/admin/* routes
			return roleSegment === 'admin';
		case 'trainer':
			// Trainer can only access /dashboard/trainer/* routes
			return roleSegment === 'trainer';
		case 'user':
		default:
			// User can only access /dashboard/user/* routes
			return roleSegment === 'user';
	}
}

/**
 * Get the correct dashboard URL for a user role
 */
export function getDashboardUrlForRole(role: UserRole): string {
	switch (role) {
		case 'admin':
			return '/dashboard/admin';
		case 'trainer':
			return '/dashboard/trainer';
		case 'user':
		default:
			return '/dashboard/user';
	}
}

/**
 * Get allowed route patterns for a role
 */
export function getAllowedRoutePatterns(role: UserRole): string[] {
	const basePatterns = [
		'/login',
		'/register',
		'/logout',
		'/dashboard', // Redirects to role-specific dashboard
		'/' // Landing page
	];

	switch (role) {
		case 'admin':
			return [...basePatterns, '/dashboard/admin', '/dashboard/admin/*'];
		case 'trainer':
			return [...basePatterns, '/dashboard/trainer', '/dashboard/trainer/*'];
		case 'user':
		default:
			return [...basePatterns, '/dashboard/user', '/dashboard/user/*'];
	}
}

/**
 * Get error message for unauthorized access
 */
export function getUnauthorizedMessage(userRole: UserRole, attemptedRoute: string): string {
	const roleNames = {
		admin: 'Administrator',
		trainer: 'Trainer',
		user: 'User'
	};

	return `Access denied. ${roleNames[userRole]} accounts cannot access ${attemptedRoute}. You will be redirected to your dashboard.`;
}

/**
 * Simple redirect to dashboard - let the dashboard layout figure out the role-specific routing
 */
export function redirectToDashboard(): string {
	return '/dashboard';
}
