import type { AuthResult } from './types';
import type { LoginFormData, RegisterFormData } from '$lib/schemas/auth';
import { authStore } from '$lib/stores/auth.store';
import { PUBLIC_API_URL, PUBLIC_API_BASE } from '$env/static/public';

export class AuthClient {
	private baseUrl: string;
	private fetch: typeof fetch;

	constructor(customFetch?: typeof fetch) {
		this.fetch = customFetch || fetch;

		const apiBase = PUBLIC_API_BASE;
		const apiUrl = PUBLIC_API_URL;

		if (!apiUrl || !apiBase) {
			throw new Error('Missing required environment variables: PUBLIC_API_URL or PUBLIC_API_BASE');
		}

		// In development, use apiBase. In production, use full URL + apiBase
		this.baseUrl = import.meta.env?.MODE === 'development' ? apiBase : `${apiUrl}${apiBase}`;

		console.log('Auth Client initialized with:', {
			apiBase,
			apiUrl,
			baseUrl: this.baseUrl,
			mode: import.meta.env?.MODE || 'unknown'
		});
	}

	async register(data: RegisterFormData): Promise<AuthResult> {
		const url = `${this.baseUrl}/auth/register`;

		console.log('Register attempt:', { url });

		const response = await this.fetch(url, {
			method: 'POST',
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
				'Sec-Fetch-Site': 'same-origin',
				'Sec-Fetch-Mode': 'cors'
			},
			body: JSON.stringify(data)
		});

		const result = await response.json();

		if (!response.ok) {
			console.error('Registration failed:', {
				status: response.status,
				headers: Object.fromEntries(response.headers.entries()),
				error: result.error
			});
			throw new Error(result.error || 'Registration failed');
		}

		return result;
	}

	async login(data: LoginFormData): Promise<AuthResult> {
		const url = `${this.baseUrl}/auth/login`;
		console.log('Login attempt:', { url });

		const response = await this.fetch(url, {
			method: 'POST',
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
				'Sec-Fetch-Site': 'same-origin',
				'Sec-Fetch-Mode': 'cors'
			},
			body: JSON.stringify(data)
		});

		const result = await response.json();

		if (!response.ok) {
			console.error('Login failed:', {
				status: response.status,
				headers: Object.fromEntries(response.headers.entries()),
				error: result.error
			});
			throw new Error(result.error || 'Authentication failed');
		}

		if (result.user) {
			authStore.setUser(result.user);
		}
		return result;
	}

	async checkSession(): Promise<AuthResult> {
		const url = `${this.baseUrl}/auth/session`;
		console.log('Checking session at:', url);

		// Debug: log available cookies in the client context
		if (typeof document !== 'undefined') {
			console.log('document.cookie:', document.cookie);
		} else {
			console.log('document is undefined (possibly running in a non-browser environment)');
			console.log('testing cookies');
		}

		const response = await this.fetch(url, {
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
				'Sec-Fetch-Site': 'same-origin',
				'Sec-Fetch-Mode': 'cors'
			}
		});

		console.log('Response status:', response.status);
		console.log(
			'Response headers:',
			JSON.stringify(Object.fromEntries(response.headers.entries()))
		);

		let result;
		const text = await response.text();
		console.log('Raw response text:', text);
		try {
			result = JSON.parse(text);
		} catch {
			// Fallback to using the raw text as error message if JSON parsing fails
			result = { error: text };
		}

		if (!response.ok) {
			authStore.clearUser();
			throw new Error(result.error || 'Session invalid');
		}

		if (result.user) {
			authStore.setUser(result.user);
		}
		return result;
	}

	async logout(): Promise<void> {
		try {
			const url = `${this.baseUrl}/auth/session`;
			console.log('Logout URL:', url); // Debug log

			const response = await this.fetch(url, {
				method: 'DELETE',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
					'Sec-Fetch-Site': 'same-origin',
					'Sec-Fetch-Mode': 'cors'
				}
			});

			if (!response.ok) {
				console.error('Logout failed:', response.status);
				throw new Error('Logout failed');
			}

			await response.json(); // Consume the response
		} catch (error) {
			console.error('Logout error:', error);
			throw error;
		} finally {
			// Always clear local state
			authStore.clearUser();
		}
	}
}

export const authClient = new AuthClient();
