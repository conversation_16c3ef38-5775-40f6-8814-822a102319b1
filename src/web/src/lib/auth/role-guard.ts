import { redirect } from '@sveltejs/kit';
import type { UserRole } from './types';
import { canAccessRoute, hasPermission, type Permission } from './permissions';

/**
 * Role-based route protection for server-side navigation
 */
export function requireRole(userRole: UserRole | undefined, requiredRoles: UserRole[]): void {
	if (!userRole || !requiredRoles.includes(userRole)) {
		throw redirect(302, '/unauthorized');
	}
}

/**
 * Permission-based route protection
 */
export function requirePermission(userRole: UserRole | undefined, permission: Permission): void {
	if (!userRole || !hasPermission(userRole, permission)) {
		throw redirect(302, '/unauthorized');
	}
}

/**
 * Route-based access control
 */
export function requireRouteAccess(userRole: UserRole | undefined, route: string): void {
	if (!userRole || !canAccessRoute(userRole, route)) {
		throw redirect(302, '/unauthorized');
	}
}

/**
 * Admin-only route protection
 */
export function requireAdmin(userRole: UserRole | undefined): void {
	requireRole(userRole, ['admin']);
}

/**
 * Trainer or Admin route protection
 */
export function requireTrainerOrAdmin(userRole: UserRole | undefined): void {
	requireRole(userRole, ['trainer', 'admin']);
}

/**
 * Any authenticated user route protection
 */
export function requireAuthenticated(userRole: UserRole | undefined): void {
	if (!userRole) {
		throw redirect(302, '/login');
	}
}

/**
 * Redirect users to appropriate dashboard based on role
 */
export function redirectToDashboard(userRole: UserRole): never {
	switch (userRole) {
		case 'admin':
			throw redirect(302, '/app/admin');
		case 'trainer':
			throw redirect(302, '/app/trainer');
		case 'user':
		default:
			throw redirect(302, '/app/dashboard');
	}
}

/**
 * Get the default route for a user role
 */
export function getDefaultRoute(userRole: UserRole): string {
	switch (userRole) {
		case 'admin':
			return '/app/admin';
		case 'trainer':
			return '/app/trainer';
		case 'user':
		default:
			return '/app/dashboard';
	}
}
