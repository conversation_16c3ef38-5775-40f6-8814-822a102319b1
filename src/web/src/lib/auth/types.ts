import type { LoginFormData } from '$lib/schemas/auth';

export type Credentials = LoginFormData;

// User role types
export type UserRole = 'user' | 'trainer' | 'admin';

// Internal user type with sensitive data
export type User = {
	id: string;
	email: string;
	password_hash: string;
	name: string;
	role: UserRole;
};

// Public user type without sensitive data
export type LoggedInUser = Omit<User, 'password_hash'>;

// Session type
export type Session = {
	id: string;
	userId: string;
	expiresAt: string;
	createdAt: string;
};

export type AuthResult = {
	success: boolean;
	error?: string;
	user?: LoggedInUser; // Use LoggedInUser instead of User
};
