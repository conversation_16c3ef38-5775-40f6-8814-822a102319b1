import { randomUUID } from 'crypto';
import type { User, Session, UserRole } from './types';
import { prisma } from '$lib/infrastructure/db';
import type { DogSchoolMemberRole } from '@prisma/client';

export class AuthRepository {
	async createUser(userData: {
		email: string;
		password_hash: string;
		name: string;
		role: UserRole;
	}): Promise<User> {
		const newUser = await prisma.user.create({
			data: {
				...userData,
				id: randomUUID()
			}
		});

		return newUser as User;
	}

	async findUserByEmail(email: string): Promise<User | undefined> {
		const foundUser = await prisma.user.findUnique({
			where: { email }
		});

		return foundUser as User | undefined;
	}

	async createSession(userId: string): Promise<Session> {
		const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

		const newSession = await prisma.session.create({
			data: {
				id: randomUUID(),
				userId,
				expiresAt
			}
		});

		return {
			...newSession,
			expiresAt: newSession.expiresAt.toISOString(),
			createdAt: newSession.createdAt.toISOString()
		};
	}

	async getSession(sessionId: string): Promise<Session | undefined> {
		const session = await prisma.session.findUnique({
			where: { id: sessionId }
		});

		if (!session) return undefined;

		// Check if session is expired
		if (session.expiresAt < new Date()) {
			// Delete expired session from database
			await prisma.session.delete({
				where: { id: sessionId }
			});
			return undefined;
		}

		return {
			...session,
			expiresAt: session.expiresAt.toISOString(),
			createdAt: session.createdAt.toISOString()
		};
	}

	async deleteSession(sessionId: string): Promise<void> {
		await prisma.session.delete({
			where: { id: sessionId }
		});
	}

	async getUserById(userId: string): Promise<User | undefined> {
		const foundUser = await prisma.user.findUnique({
			where: { id: userId }
		});

		return foundUser as User | undefined;
	}

	async createDogSchool(dogSchoolData: {
		name: string;
		userId: string;
	}): Promise<{ id: string; name: string }> {
		const newDogSchool = await prisma.dogSchool.create({
			data: {
				id: randomUUID(),
				name: dogSchoolData.name,
				slug: this.generateSlug(dogSchoolData.name),
				isActive: true
			}
		});

		return {
			id: newDogSchool.id,
			name: newDogSchool.name
		};
	}

	async createDogSchoolMember(memberData: {
		userId: string;
		dogSchoolId: string;
		role: DogSchoolMemberRole;
	}): Promise<void> {
		await prisma.dogSchoolMember.create({
			data: {
				id: randomUUID(),
				userId: memberData.userId,
				dogSchoolId: memberData.dogSchoolId,
				role: memberData.role
			}
		});
	}

	private generateSlug(name: string): string {
		return name
			.toLowerCase()
			.replace(/[^a-z0-9]+/g, '-')
			.replace(/^-+|-+$/g, '');
	}
}
