import { AuthRepository } from './auth.repository';
import type { User, LoggedInUser } from './types';
import { hash, verify } from 'argon2';
import { loginRateLimiter } from '$lib/utils/rate-limiter';
import { logger } from '$lib/utils/logger';

export class AuthServer {
	constructor(private repository: AuthRepository) { }

	async register(userData: {
		email: string;
		password: string;
		name: string;
		role?: 'user' | 'trainer';
		dogSchoolName?: string;
	}): Promise<User> {
		// Use Argon2id with secure defaults
		const password_hash = await hash(userData.password, {
			memoryCost: 19456, // 19 MB (recommended)
			timeCost: 2, // 2 iterations
			hashLength: 32 // 32 bytes output
		});

		const user = await this.repository.createUser({
			email: userData.email,
			name: userData.name,
			password_hash,
			role: userData.role || 'user'
		});

		// If user is a trainer and has a dog school name, create the dog school
		if (userData.role === 'trainer' && userData.dogSchoolName) {
			const dogSchool = await this.repository.createDogSchool({
				name: userData.dogSchoolName,
				userId: user.id
			});

			// Create the trainer as an owner of the dog school
			await this.repository.createDogSchoolMember({
				userId: user.id,
				dogSchoolId: dogSchool.id,
				role: 'owner'
			});
		}

		return user;
	}

	async login(
		credentials: { email: string; password: string },
		clientIp?: string
	): Promise<{ user: LoggedInUser; sessionId: string } | null> {
		// Input validation
		if (!credentials.email || !credentials.password) {
			return null;
		}

		// Sanitize email
		const email = credentials.email.toLowerCase().trim();

		// Rate limiting by IP and email
		const rateLimitKey = clientIp ? `${clientIp}:${email}` : email;

		if (loginRateLimiter.isRateLimited(rateLimitKey)) {
			const remainingTime = loginRateLimiter.getRemainingTime(rateLimitKey);
			logger.warn('Login rate limited', {
				email,
				clientIp,
				remainingTime: Math.ceil(remainingTime / 1000)
			});
			return null;
		}

		const user = await this.repository.findUserByEmail(email);
		if (!user) {
			// Record failed attempt
			loginRateLimiter.recordAttempt(rateLimitKey);
			// Constant time delay to prevent timing attacks
			await new Promise((resolve) => setTimeout(resolve, 100));
			return null;
		}

		const isPasswordValid = await verify(user.password_hash, credentials.password);
		if (!isPasswordValid) {
			// Record failed attempt
			loginRateLimiter.recordAttempt(rateLimitKey);
			// Constant time delay to prevent timing attacks
			await new Promise((resolve) => setTimeout(resolve, 100));
			return null;
		}

		// Successful login - create session
		const session = await this.repository.createSession(user.id);

		// Transform to LoggedInUser before returning
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const { password_hash: _ph, ...loggedInUser } = user;

		logger.info('Successful login', { userId: user.id, email });
		return { user: loggedInUser, sessionId: session.id };
	}

	async validateSession(sessionId: string): Promise<{ user: LoggedInUser } | null> {
		const session = await this.repository.getSession(sessionId);
		if (!session) return null;

		const user = await this.repository.getUserById(session.userId);
		if (!user) return null;
		// Transform to LoggedInUser before returning
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const { password_hash: _ph, ...loggedInUser } = user;
		return { user: loggedInUser };
	}

	async logout(sessionId: string): Promise<void> {
		await this.repository.deleteSession(sessionId);
	}
}
